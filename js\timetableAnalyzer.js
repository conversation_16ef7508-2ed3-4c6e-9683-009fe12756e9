/**
 * Timetable Analyzer Module (Lazy Loaded)
 * Handles Socket.IO connection and timetable data processing
 */

class TimetableAnalyzer {
    constructor() {
        this.socket = null;
        this.initialized = false;
    }

    /**
     * Initialize the timetable analyzer
     */
    async initialize() {
        if (this.initialized) return;

        try {
            this.socket = io();
            this.setupSocketListeners();
            this.initialized = true;
            console.log('✅ Timetable Analyzer module loaded');
        } catch (error) {
            console.error('❌ Timetable Analyzer initialization failed:', error);
            throw error;
        }
    }

    /**
     * Setup Socket.IO event listeners
     */
    setupSocketListeners() {
        if (!this.socket) return;

        // Listen for timetable analysis updates
        this.socket.on('timetableData', (data) => {
            if (data.type === 'timetableData') {
                // Update the UI with the new timetable data
                if (data.content) {
                    this.updateTimetableDisplay(data.content);
                }
            }
        });

        this.socket.on('timetableAnalysisError', (data) => {
            this.showErrorToast(data.error || 'An error occurred during timetable analysis');
        });
    }

    /**
     * Handle timetable upload
     */
    async handleTimetableUpload(event) {
        await this.initialize();

        const file = event.target.files[0];
        if (!file) return;

        try {
            // Show loading state
            this.showAnalysisLoading('timetableAnalysis', 'Analyzing timetable...');

            // Convert file to base64
            const imageData = await this.fileToBase64(file);

            // Send to server for analysis
            this.socket.emit('analyzeTimetable', {
                imageData: imageData,
                fileName: file.name
            });

            // Show preview
            this.showImagePreview(imageData, 'timetablePreview');

        } catch (error) {
            console.error('Error uploading timetable:', error);
            this.showErrorToast('Failed to upload timetable. Please try again.');
        }
    }

    /**
     * Convert file to base64
     */
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(file);
        });
    }

    /**
     * Show image preview
     */
    showImagePreview(imageData, containerId) {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <img src="${imageData}" alt="Timetable Preview" class="img-fluid rounded" style="max-height: 300px;">
            `;
        }
    }

    /**
     * Group events by day of the week
     */
    groupEventsByDay(events) {
        const grouped = {};
        const dayMap = {
            'monday': 1,
            'tuesday': 2,
            'wednesday': 3,
            'thursday': 4,
            'friday': 5,
            'saturday': 6,
            'sunday': 0
        };

        events.forEach(event => {
            if (event.recurring) {
                const day = event.recurring.dayOfWeek.toLowerCase();
                // Create array for this day if it doesn't exist
                if (!grouped[day]) {
                    grouped[day] = [];
                }
                // Sort events by their actual day number to ensure correct order
                grouped[day].push({
                    ...event,
                    dayNumber: dayMap[day]
                });
            }
        });

        // Sort each day's events by start time
        Object.keys(grouped).forEach(day => {
            grouped[day].sort((a, b) => {
                // First sort by day number
                if (a.dayNumber !== b.dayNumber) {
                    return a.dayNumber - b.dayNumber;
                }
                // Then sort by start time
                return a.startTime.localeCompare(b.startTime);
            });
        });

        return grouped;
    }

    /**
     * Update timetable display with data
     */
    updateTimetableDisplay(timetableData) {
        const analysisDiv = document.getElementById('timetableAnalysis');
        if (!analysisDiv) return;

        analysisDiv.style.display = 'block';

        // Update schedule container
        const scheduleContainer = document.querySelector('.schedule-container');
        if (scheduleContainer) {
            let scheduleHtml = '';
            const groupedByDay = this.groupEventsByDay(timetableData);

            // Define the order of days
            const dayOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];

            // Iterate through days in order
            dayOrder.forEach(day => {
                if (groupedByDay[day] && groupedByDay[day].length > 0) {
                    scheduleHtml += `
                        <div class="day-schedule">
                            <h6>${this.capitalizeFirstLetter(day)}</h6>
                            <ul class="list-unstyled">
                    `;
                    groupedByDay[day].forEach(event => {
                        const type = event.type === 'class' ? 'class-slot' : 'free-slot';
                        scheduleHtml += `
                            <li class="${type} mb-2">
                                ${event.type === 'class' ? event.subject : 'Free Time'}:
                                ${event.startTime} - ${event.endTime}
                            </li>
                        `;
                    });
                    scheduleHtml += `</ul></div>`;
                }
            });

            scheduleContainer.innerHTML = scheduleHtml;
        }
    }

    /**
     * Show error toast
     */
    showErrorToast(message) {
        const errorToast = document.getElementById('errorToast');
        if (errorToast) {
            const toastBody = errorToast.querySelector('.toast-body');
            if (toastBody) {
                toastBody.textContent = message;
                const toast = new bootstrap.Toast(errorToast);
                toast.show();
            }
        }
    }

    /**
     * Show analysis loading state
     */
    showAnalysisLoading(containerId, message = 'Analyzing timetable...') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="d-flex justify-content-center align-items-center p-4">
                    <div class="spinner-border text-primary me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>${message}</span>
                </div>
            `;
            container.style.display = 'block';
        }
    }

    /**
     * Process timetable image
     */
    async processTimetableImage(file) {
        try {
            const formData = new FormData();
            formData.append('image', file);

            // Upload the image first
            const uploadResponse = await fetch('/upload', {
                method: 'POST',
                body: formData
            });
            const uploadResult = await uploadResponse.json();

            if (!uploadResult.success) {
                throw new Error(uploadResult.error || 'Failed to upload image');
            }

            // Analyze the uploaded image
            const analysisResponse = await fetch('/api/analyze-timetable', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ imagePath: uploadResult.filePath })
            });
            const analysis = await analysisResponse.json();

            if (!analysis.success) {
                throw new Error(analysis.error || 'Failed to analyze timetable');
            }

            // Display results
            const analysisDiv = document.getElementById('timetableAnalysis');
            analysisDiv.style.display = 'block';

            // Display schedule
            const scheduleContainer = document.querySelector('.schedule-container');
            let scheduleHtml = '';
            for (const [day, slots] of Object.entries(analysis.schedule)) {
                scheduleHtml += `
                    <div class="day-schedule">
                        <h6>${day.charAt(0).toUpperCase() + day.slice(1)}</h6>
                        <ul class="list-unstyled">
                `;
                slots.forEach(slot => {
                    const type = slot.type === 'class' ? 'class-slot' : 'free-slot';
                    scheduleHtml += `
                        <li class="${type} mb-2">
                            ${slot.type === 'class' ? slot.subject : 'Free Time'}:
                            ${slot.start} - ${slot.end}
                            (${slot.duration} hours)
                        </li>
                    `;
                });
                scheduleHtml += `</ul></div>`;
            }
            scheduleContainer.innerHTML = scheduleHtml;

            // Display free time analysis
            const freeTimeContainer = document.querySelector('.free-time-container');
            let freeTimeHtml = '<div class="daily-free-time">';
            for (const [day, data] of Object.entries(analysis.summary.dailyFreeTime)) {
                freeTimeHtml += `
                    <div class="day-free-time mb-3">
                        <h6>${day.charAt(0).toUpperCase() + day.slice(1)}</h6>
                        <p class="mb-2">Total Free Hours: ${data.hours}</p>
                        <ul class="list-unstyled">
                            ${data.slots.map(slot => `<li class="mb-1">${slot}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }
            freeTimeHtml += '</div>';
            freeTimeContainer.innerHTML = freeTimeHtml;

            // Display weekly stats
            const statsContainer = document.querySelector('.stats-container');
            statsContainer.innerHTML = `
                <ul class="list-unstyled">
                    <li class="mb-2">Busiest Day: ${analysis.weeklyStats.busiest_day}</li>
                    <li class="mb-2">Most Free Time: ${analysis.weeklyStats.lightest_day}</li>
                    <li class="mb-2">Total Class Hours: ${analysis.weeklyStats.total_class_hours}</li>
                    <li class="mb-2">Total Free Hours: ${analysis.weeklyStats.total_free_hours}</li>
                    <li class="mb-2">Best Study Days: ${analysis.weeklyStats.best_study_days.join(', ')}</li>
                </ul>
            `;

            // Display recommendations
            const recommendationsContainer = document.querySelector('.recommendations-container');
            recommendationsContainer.innerHTML = `
                <div class="study-tips mb-4">
                    <h6>Study Tips</h6>
                    <ul class="list-unstyled">
                        ${analysis.recommendations.study_tips.map(tip => `<li class="mb-2">${tip}</li>`).join('')}
                    </ul>
                </div>
                <div class="break-tips">
                    <h6>Break Management</h6>
                    <ul class="list-unstyled">
                        ${analysis.recommendations.break_management.map(tip => `<li class="mb-2">${tip}</li>`).join('')}
                    </ul>
                </div>
            `;

        } catch (error) {
            console.error('Error analyzing timetable:', error);
            this.showErrorToast('Failed to analyze timetable. Please try again.');
        }
    }

// Initialize timetable analyzer
function initializeTimetableAnalyzer() {
    // Initialize Socket.IO connection
    const socket = initializeSocketConnection();
    
    // Set up timetable input event listener
    const timetableInput = document.getElementById('timetableInput');
    if (timetableInput) {
        timetableInput.addEventListener('change', handleTimetableUpload);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initializeTimetableAnalyzer);
