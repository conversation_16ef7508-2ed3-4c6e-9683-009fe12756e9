/**
 * UI Core Module
 * Handles UI initialization and common UI operations
 */

class UICore {
    constructor() {
        this.initialized = false;
        this.viewMode = 'grid'; // grid or list
    }

    /**
     * Initialize UI components
     */
    async initializeUI() {
        if (this.initialized) return;

        try {
            this.setupViewToggle();
            this.setupModalHandlers();
            this.setupFormValidation();
            this.setupTooltips();
            this.loadUserPreferences();
            
            this.initialized = true;
            console.log('✅ UI Core initialized');
        } catch (error) {
            console.error('❌ UI Core initialization failed:', error);
        }
    }

    /**
     * Setup view toggle (grid/list)
     */
    setupViewToggle() {
        const gridViewBtn = document.getElementById('gridViewBtn');
        const listViewBtn = document.getElementById('listViewBtn');

        if (gridViewBtn && listViewBtn) {
            gridViewBtn.addEventListener('click', () => this.setViewMode('grid'));
            listViewBtn.addEventListener('click', () => this.setViewMode('list'));
        }
    }

    /**
     * Set view mode (grid or list)
     */
    setViewMode(mode) {
        this.viewMode = mode;
        const container = document.getElementById('studySpacesContainer');
        const gridBtn = document.getElementById('gridViewBtn');
        const listBtn = document.getElementById('listViewBtn');

        if (container) {
            container.className = mode === 'grid' ? 'row' : 'list-view';
        }

        // Update button states
        if (gridBtn && listBtn) {
            gridBtn.classList.toggle('active', mode === 'grid');
            listBtn.classList.toggle('active', mode === 'list');
        }

        // Save preference
        localStorage.setItem('studySpacesViewMode', mode);
    }

    /**
     * Setup modal handlers
     */
    setupModalHandlers() {
        // API Settings Modal
        const apiSettingsBtn = document.getElementById('apiSettingsBtn');
        const apiSettingsModal = document.getElementById('apiSettingsModal');

        if (apiSettingsBtn && apiSettingsModal) {
            apiSettingsBtn.addEventListener('click', () => {
                const modal = new bootstrap.Modal(apiSettingsModal);
                modal.show();
            });
        }

        // Help Modal
        const helpBtn = document.getElementById('helpBtn');
        if (helpBtn) {
            helpBtn.addEventListener('click', () => {
                const helpModal = document.getElementById('helpModal');
                if (helpModal) {
                    const modal = new bootstrap.Modal(helpModal);
                    modal.show();
                }
            });
        }

        // Toggle API key visibility
        const toggleApiKeyBtn = document.getElementById('toggleApiKeyBtn');
        if (toggleApiKeyBtn) {
            toggleApiKeyBtn.addEventListener('click', this.toggleApiKeyVisibility);
        }
    }

    /**
     * Toggle API key visibility
     */
    toggleApiKeyVisibility() {
        const input = document.getElementById('geminiApiKey');
        const icon = document.querySelector('#toggleApiKeyBtn i');
        
        if (input && icon) {
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.replace('bi-eye', 'bi-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.replace('bi-eye-slash', 'bi-eye');
            }
        }
    }

    /**
     * Setup form validation
     */
    setupFormValidation() {
        // Study space form validation
        const saveBtn = document.getElementById('saveStudySpace');
        if (saveBtn) {
            saveBtn.addEventListener('click', (e) => {
                if (!this.validateStudySpaceForm()) {
                    e.preventDefault();
                    return false;
                }
            });
        }

        // Real-time validation
        const requiredFields = ['spaceName', 'spaceLocation'];
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.addEventListener('blur', () => this.validateField(field));
                field.addEventListener('input', () => this.clearFieldError(field));
            }
        });
    }

    /**
     * Validate study space form
     */
    validateStudySpaceForm() {
        const spaceName = document.getElementById('spaceName');
        const spaceLocation = document.getElementById('spaceLocation');
        let isValid = true;

        if (spaceName && !spaceName.value.trim()) {
            this.showFieldError(spaceName, 'Space name is required');
            isValid = false;
        }

        if (spaceLocation && !spaceLocation.value.trim()) {
            this.showFieldError(spaceLocation, 'Location is required');
            isValid = false;
        }

        return isValid;
    }

    /**
     * Validate individual field
     */
    validateField(field) {
        if (!field.value.trim()) {
            this.showFieldError(field, `${field.labels[0]?.textContent || 'This field'} is required`);
            return false;
        }
        this.clearFieldError(field);
        return true;
    }

    /**
     * Show field error
     */
    showFieldError(field, message) {
        field.classList.add('is-invalid');
        
        // Remove existing error message
        const existingError = field.parentNode.querySelector('.invalid-feedback');
        if (existingError) {
            existingError.remove();
        }

        // Add new error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;
        field.parentNode.appendChild(errorDiv);
    }

    /**
     * Clear field error
     */
    clearFieldError(field) {
        field.classList.remove('is-invalid');
        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    /**
     * Setup tooltips
     */
    setupTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    /**
     * Load user preferences
     */
    loadUserPreferences() {
        // Load view mode preference
        const savedViewMode = localStorage.getItem('studySpacesViewMode');
        if (savedViewMode) {
            this.setViewMode(savedViewMode);
        }

        // Load theme preference
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
        }
    }

    /**
     * Show loading state
     */
    showLoading(element, message = 'Loading...') {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }

        if (element) {
            element.innerHTML = `
                <div class="d-flex justify-content-center align-items-center p-4">
                    <div class="spinner-border text-primary me-2" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <span>${message}</span>
                </div>
            `;
        }
    }

    /**
     * Hide loading state
     */
    hideLoading(element) {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }

        if (element) {
            const loadingDiv = element.querySelector('.d-flex.justify-content-center');
            if (loadingDiv) {
                loadingDiv.remove();
            }
        }
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        const toastContainer = document.querySelector('.toast-container') || this.createToastContainer();
        
        const toastId = `toast-${Date.now()}`;
        const toastHtml = `
            <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-${type} text-white">
                    <i class="bi bi-info-circle me-2"></i>
                    <strong class="me-auto">Notification</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    /**
     * Create toast container if it doesn't exist
     */
    createToastContainer() {
        const container = document.createElement('div');
        container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '11';
        document.body.appendChild(container);
        return container;
    }

    /**
     * Animate element entrance
     */
    animateIn(element, animation = 'fadeIn') {
        if (typeof element === 'string') {
            element = document.getElementById(element);
        }

        if (element) {
            element.classList.add('animate__animated', `animate__${animation}`);
            
            // Remove animation classes after animation completes
            element.addEventListener('animationend', () => {
                element.classList.remove('animate__animated', `animate__${animation}`);
            }, { once: true });
        }
    }

    /**
     * Get current view mode
     */
    getViewMode() {
        return this.viewMode;
    }
}

// Create singleton instance
const uiCore = new UICore();

// Export the main functions
export const initializeUI = () => uiCore.initializeUI();
export const showLoading = (element, message) => uiCore.showLoading(element, message);
export const hideLoading = (element) => uiCore.hideLoading(element);
export const showToast = (message, type) => uiCore.showToast(message, type);
export const animateIn = (element, animation) => uiCore.animateIn(element, animation);
export const setViewMode = (mode) => uiCore.setViewMode(mode);
export const getViewMode = () => uiCore.getViewMode();

// Export the core instance
export { uiCore };
