/**
 * API Settings Module (Lazy Loaded)
 * Handles API key management and settings
 */

class APISettings {
    constructor() {
        this.initialized = false;
    }

    /**
     * Initialize API settings
     */
    initialize() {
        if (this.initialized) return;
        
        this.setupEventListeners();
        this.initialized = true;
        console.log('✅ API Settings module loaded');
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        const saveBtn = document.getElementById('saveApiKeyBtn');
        const toggleBtn = document.getElementById('toggleApiKeyBtn');

        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveApiKey());
        }

        if (toggleBtn) {
            toggleBtn.addEventListener('click', () => this.toggleApiKeyVisibility());
        }
    }

    /**
     * Open API settings modal
     */
    openApiSettings() {
        this.initialize();
        
        const modalElement = document.getElementById('apiSettingsModal');
        if (!modalElement) return;

        let modal = bootstrap.Modal.getInstance(modalElement);
        if (!modal) {
            modal = new bootstrap.Modal(modalElement);
        }

        // Load saved API key if exists
        const savedKey = localStorage.getItem('geminiApiKey');
        const keyInput = document.getElementById('geminiApiKey');
        if (savedKey && keyInput) {
            keyInput.value = savedKey;
        }

        modal.show();
    }

    /**
     * Toggle API key visibility
     */
    toggleApiKeyVisibility() {
        const input = document.getElementById('geminiApiKey');
        const icon = document.querySelector('#toggleApiKeyBtn i');
        
        if (input && icon) {
            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.replace('bi-eye', 'bi-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.replace('bi-eye-slash', 'bi-eye');
            }
        }
    }

    /**
     * Save and test API key
     */
    async saveApiKey() {
        const apiKey = document.getElementById('geminiApiKey')?.value.trim();
        const statusDiv = document.getElementById('apiKeyStatus');

        if (!apiKey) {
            this.showStatus('Please enter an API key', 'danger');
            return;
        }

        try {
            this.showStatus('Testing API key...', 'info');

            // Test the API key
            const response = await fetch('/api/test-api-key', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ apiKey })
            });

            const result = await response.json();

            if (result.success) {
                // Save to localStorage
                localStorage.setItem('geminiApiKey', apiKey);
                this.showStatus('API key saved and tested successfully!', 'success');
                
                // Close modal after 2 seconds
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('apiSettingsModal'));
                    if (modal) modal.hide();
                }, 2000);
            } else {
                this.showStatus(result.error || 'Invalid API key', 'danger');
            }
        } catch (error) {
            console.error('Error testing API key:', error);
            this.showStatus('Error testing API key. Please try again.', 'danger');
        }
    }

    /**
     * Show status message
     */
    showStatus(message, type) {
        const statusDiv = document.getElementById('apiKeyStatus');
        if (!statusDiv) return;

        statusDiv.className = `alert alert-${type}`;
        statusDiv.textContent = message;
        statusDiv.style.display = 'block';

        // Hide after 5 seconds for non-success messages
        if (type !== 'success') {
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 5000);
        }
    }

    /**
     * Get saved API key
     */
    getApiKey() {
        return localStorage.getItem('geminiApiKey');
    }

    /**
     * Check if API key is configured
     */
    isApiKeyConfigured() {
        const apiKey = this.getApiKey();
        return apiKey && apiKey.length > 0;
    }

    /**
     * Validate API key format
     */
    validateApiKeyFormat(apiKey) {
        // Basic validation for Gemini API key format
        return apiKey && typeof apiKey === 'string' && apiKey.length > 20;
    }
}

// Create singleton instance
const apiSettings = new APISettings();

// Export functions
export const openApiSettings = () => apiSettings.openApiSettings();
export const saveApiKey = () => apiSettings.saveApiKey();
export const toggleApiKeyVisibility = () => apiSettings.toggleApiKeyVisibility();
export const getApiKey = () => apiSettings.getApiKey();
export const isApiKeyConfigured = () => apiSettings.isApiKeyConfigured();

// Export the instance
export { apiSettings };
