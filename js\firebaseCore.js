/**
 * Firebase Core Module
 * Centralized Firebase operations without global pollution
 */

import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { getFirestore, collection, doc, setDoc, getDoc, getDocs, deleteDoc } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
import { getAuth, onAuthStateChanged } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
import { getStorage, ref, uploadString, getDownloadURL } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-storage.js';

class FirebaseCore {
    constructor() {
        this.app = null;
        this.db = null;
        this.auth = null;
        this.storage = null;
        this.user = null;
        this.initialized = false;
    }

    /**
     * Initialize Firebase with configuration
     */
    async initialize() {
        if (this.initialized) return;

        const firebaseConfig = {
            apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
            authDomain: "mzm-gpace.firebaseapp.com",
            projectId: "mzm-gpace",
            storageBucket: "mzm-gpace.firebasestorage.app",
            messagingSenderId: "949014366726",
            appId: "1:949014366726:web:3aa05a6e133e2066c45187",
        };

        try {
            this.app = initializeApp(firebaseConfig);
            this.db = getFirestore(this.app);
            this.auth = getAuth(this.app);
            this.storage = getStorage(this.app);
            
            this.initialized = true;
            console.log('✅ Firebase Core initialized');
        } catch (error) {
            console.error('❌ Firebase initialization failed:', error);
            throw error;
        }
    }

    /**
     * Initialize authentication and set up auth state listener
     */
    async initializeAuth() {
        if (!this.initialized) await this.initialize();

        return new Promise((resolve) => {
            onAuthStateChanged(this.auth, (user) => {
                this.user = user;
                if (user) {
                    console.log('✅ User authenticated:', user.uid);
                } else {
                    console.log('ℹ️ User not authenticated');
                }
                resolve(user);
            });
        });
    }

    /**
     * Save study spaces to Firestore
     */
    async saveStudySpaces(studySpaces) {
        if (!this.user) {
            console.error('No user is signed in');
            return false;
        }

        try {
            // Process images first - upload base64 images to Firebase Storage
            const processedSpaces = await Promise.all(studySpaces.map(async (space) => {
                if (space.image && space.image.startsWith('data:image')) {
                    try {
                        const imageId = `${space.id}-${Date.now()}`;
                        const storageRef = ref(this.storage, `users/${this.user.uid}/study-spaces/${imageId}`);
                        
                        await uploadString(storageRef, space.image, 'data_url');
                        const downloadURL = await getDownloadURL(storageRef);
                        
                        return { ...space, image: downloadURL };
                    } catch (error) {
                        console.error('Error uploading image:', error);
                        return space; // Return original space if image upload fails
                    }
                }
                return space;
            }));

            const docRef = doc(this.db, 'users', this.user.uid, 'data', 'studySpaces');
            await setDoc(docRef, { 
                spaces: processedSpaces,
                lastUpdated: new Date().toISOString()
            });

            console.log('✅ Study spaces saved to Firestore');
            return true;
        } catch (error) {
            console.error('❌ Error saving study spaces:', error);
            return false;
        }
    }

    /**
     * Load study spaces from Firestore
     */
    async loadStudySpaces() {
        if (!this.user) {
            console.log('ℹ️ No user signed in, cannot load study spaces');
            return null;
        }

        try {
            const docRef = doc(this.db, 'users', this.user.uid, 'data', 'studySpaces');
            const docSnap = await getDoc(docRef);

            if (docSnap.exists()) {
                const data = docSnap.data();
                console.log('✅ Study spaces loaded from Firestore');
                return data.spaces || [];
            } else {
                console.log('ℹ️ No study spaces found in Firestore');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading study spaces:', error);
            return null;
        }
    }

    /**
     * Delete a study space from Firestore
     */
    async deleteStudySpace(spaceId, studySpaces) {
        if (!this.user) {
            console.error('No user is signed in');
            return false;
        }

        try {
            // Remove the space from the array
            const updatedSpaces = studySpaces.filter(space => space.id !== spaceId);
            
            // Save the updated array
            const success = await this.saveStudySpaces(updatedSpaces);
            
            if (success) {
                console.log('✅ Study space deleted from Firestore');
            }
            
            return success;
        } catch (error) {
            console.error('❌ Error deleting study space:', error);
            return false;
        }
    }

    /**
     * Force sync study spaces
     */
    async forceSyncStudySpaces() {
        if (!this.user) {
            console.log('ℹ️ No user signed in, cannot sync');
            return null;
        }

        try {
            // Get fresh data from Firestore
            const spaces = await this.loadStudySpaces();
            
            if (spaces) {
                // Update local storage
                localStorage.setItem('studySpaces', JSON.stringify(spaces));
                console.log('✅ Study spaces synced successfully');
            }
            
            return spaces;
        } catch (error) {
            console.error('❌ Error syncing study spaces:', error);
            return null;
        }
    }

    /**
     * Get current user
     */
    getCurrentUser() {
        return this.user;
    }

    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return !!this.user;
    }
}

// Create singleton instance
const firebaseCore = new FirebaseCore();

// Export functions that replace the global window assignments
export const saveStudySpacesToFirestore = (studySpaces) => firebaseCore.saveStudySpaces(studySpaces);
export const loadStudySpacesFromFirestore = () => firebaseCore.loadStudySpaces();
export const deleteStudySpaceFromFirestore = (spaceId, studySpaces) => firebaseCore.deleteStudySpace(spaceId, studySpaces);
export const forceSyncStudySpaces = () => firebaseCore.forceSyncStudySpaces();

// Export the core instance for advanced usage
export { firebaseCore };

// Initialize auth when module loads
export const initializeAuth = () => firebaseCore.initializeAuth();
