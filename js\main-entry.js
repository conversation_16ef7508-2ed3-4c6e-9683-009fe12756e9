/**
 * Main Entry Point for Study Spaces Application
 * Replaces inline JavaScript and manages module loading
 */

class StudySpacesApp {
    constructor() {
        this.modules = new Map();
        this.loadingPromises = new Map();
        this.initialized = false;
    }

    /**
     * Initialize the application
     */
    async init() {
        if (this.initialized) return;
        
        try {
            console.log('🚀 Initializing Study Spaces App...');
            
            // Load core modules first
            await this.loadCoreModules();
            
            // Initialize UI components
            await this.initializeUI();
            
            // Setup event listeners
            this.setupEventListeners();
            
            // Load user data
            await this.loadUserData();
            
            this.initialized = true;
            console.log('✅ Study Spaces App initialized successfully');
            
        } catch (error) {
            console.error('❌ Failed to initialize Study Spaces App:', error);
            this.showErrorMessage('Failed to load application. Please refresh the page.');
        }
    }

    /**
     * Load core modules that are always needed
     */
    async loadCoreModules() {
        const coreModules = [
            { name: 'firebaseCore', path: './firebaseCore.js' },
            { name: 'studySpacesCore', path: './studySpacesCore.js' },
            { name: 'uiCore', path: './uiCore.js' }
        ];

        const loadPromises = coreModules.map(async (module) => {
            const moduleInstance = await import(module.path);
            this.modules.set(module.name, moduleInstance);
            return moduleInstance;
        });

        await Promise.all(loadPromises);
    }

    /**
     * Lazy load modules only when needed
     */
    async loadModule(moduleName, modulePath) {
        if (this.modules.has(moduleName)) {
            return this.modules.get(moduleName);
        }

        if (this.loadingPromises.has(moduleName)) {
            return this.loadingPromises.get(moduleName);
        }

        const loadPromise = import(modulePath).then(module => {
            this.modules.set(moduleName, module);
            this.loadingPromises.delete(moduleName);
            return module;
        });

        this.loadingPromises.set(moduleName, loadPromise);
        return loadPromise;
    }

    /**
     * Initialize UI components
     */
    async initializeUI() {
        const uiCore = this.modules.get('uiCore');
        if (uiCore && uiCore.initializeUI) {
            await uiCore.initializeUI();
        }
    }

    /**
     * Setup global event listeners
     */
    setupEventListeners() {
        // File upload handlers
        this.setupFileUploadHandlers();
        
        // Form submission handlers
        this.setupFormHandlers();
        
        // Navigation handlers
        this.setupNavigationHandlers();
    }

    /**
     * Setup file upload event handlers
     */
    setupFileUploadHandlers() {
        // Timetable upload
        const timetableInput = document.getElementById('timetableInput');
        if (timetableInput) {
            timetableInput.addEventListener('change', async (event) => {
                const timetableAnalyzer = await this.loadModule('timetableAnalyzer', './timetableAnalyzer.js');
                if (timetableAnalyzer.handleTimetableUpload) {
                    timetableAnalyzer.handleTimetableUpload(event);
                }
            });
        }

        // Study space image upload
        const spaceImageInput = document.getElementById('spaceImageInput');
        if (spaceImageInput) {
            spaceImageInput.addEventListener('change', async (event) => {
                const imageAnalyzer = await this.loadModule('imageAnalyzer', './imageAnalyzer.js');
                if (imageAnalyzer.handleImageUpload) {
                    imageAnalyzer.handleImageUpload(event);
                }
            });
        }
    }

    /**
     * Setup form submission handlers
     */
    setupFormHandlers() {
        // API settings form
        const saveApiKeyBtn = document.getElementById('saveApiKeyBtn');
        if (saveApiKeyBtn) {
            saveApiKeyBtn.addEventListener('click', async () => {
                const apiSettings = await this.loadModule('apiSettings', './apiSettings.js');
                if (apiSettings.saveApiKey) {
                    apiSettings.saveApiKey();
                }
            });
        }

        // Study space form
        const saveStudySpaceBtn = document.getElementById('saveStudySpace');
        if (saveStudySpaceBtn) {
            saveStudySpaceBtn.addEventListener('click', () => {
                const studySpacesCore = this.modules.get('studySpacesCore');
                if (studySpacesCore && studySpacesCore.saveStudySpace) {
                    studySpacesCore.saveStudySpace();
                }
            });
        }
    }

    /**
     * Setup navigation handlers
     */
    setupNavigationHandlers() {
        // Settings modal
        const apiSettingsBtn = document.getElementById('apiSettingsBtn');
        if (apiSettingsBtn) {
            apiSettingsBtn.addEventListener('click', async () => {
                const apiSettings = await this.loadModule('apiSettings', './apiSettings.js');
                if (apiSettings.openApiSettings) {
                    apiSettings.openApiSettings();
                }
            });
        }
    }

    /**
     * Load user data and settings
     */
    async loadUserData() {
        const firebaseCore = this.modules.get('firebaseCore');
        const studySpacesCore = this.modules.get('studySpacesCore');

        if (firebaseCore && studySpacesCore) {
            // Initialize Firebase auth
            await firebaseCore.initializeAuth();
            
            // Load study spaces
            await studySpacesCore.loadStudySpaces();
            
            // Load schedule settings
            await studySpacesCore.loadScheduleSettings();
        }
    }

    /**
     * Show error message to user
     */
    showErrorMessage(message) {
        const errorToast = document.getElementById('errorToast');
        if (errorToast) {
            const toastBody = errorToast.querySelector('.toast-body');
            if (toastBody) {
                toastBody.textContent = message;
                const toast = new bootstrap.Toast(errorToast);
                toast.show();
            }
        }
    }

    /**
     * Get module instance
     */
    getModule(moduleName) {
        return this.modules.get(moduleName);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', async () => {
    window.studySpacesApp = new StudySpacesApp();
    await window.studySpacesApp.init();
});

// Export for testing purposes
export default StudySpacesApp;
