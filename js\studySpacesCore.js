/**
 * Study Spaces Core Module
 * Main business logic for study spaces management
 */

import { saveStudySpacesToFirestore, loadStudySpacesFromFirestore, deleteStudySpaceFromFirestore } from './firebaseCore.js';

class StudySpacesCore {
    constructor() {
        this.studySpaces = [];
        this.schedule = {
            wakeTime: null,
            wakeBuffer: 0,
            sleepTime: null,
            sleepBuffer: 0
        };
        this.currentImage = null;
        this.syncStatus = {
            lastSynced: null,
            isSyncing: false,
            error: null
        };
    }

    /**
     * Initialize the study spaces core
     */
    async initialize() {
        await this.loadStudySpaces();
        await this.loadScheduleSettings();
        this.setupFileUploadHandlers();
    }

    /**
     * Load study spaces from storage
     */
    async loadStudySpaces() {
        try {
            this.setSyncStatus(true, null);
            
            // Try to load from Firestore first
            const firestoreSpaces = await loadStudySpacesFromFirestore();
            if (firestoreSpaces) {
                this.studySpaces = firestoreSpaces;
                this.setSyncStatus(false, null);
                this.syncStatus.lastSynced = new Date();
                this.displayStudySpaces();
                return;
            }

            // Fallback to localStorage
            const localSpaces = localStorage.getItem('studySpaces');
            if (localSpaces) {
                this.studySpaces = JSON.parse(localSpaces);
                this.displayStudySpaces();
            }
            
            this.setSyncStatus(false, null);
        } catch (error) {
            console.error('Error loading study spaces:', error);
            this.setSyncStatus(false, 'Failed to load study spaces');
        }
    }

    /**
     * Save a new study space
     */
    async saveStudySpace() {
        const spaceName = document.getElementById('spaceName').value.trim();
        const spaceLocation = document.getElementById('spaceLocation').value.trim();
        const spaceDescription = document.getElementById('spaceDescription').value.trim();

        if (!spaceName || !spaceLocation) {
            this.showError('Please fill in the required fields (Name and Location)');
            return;
        }

        if (!this.currentImage) {
            this.showError('Please upload an image of the study space');
            return;
        }

        // Get selected amenities
        const amenities = Array.from(document.querySelectorAll('.amenities input[type="checkbox"]:checked'))
            .map(checkbox => checkbox.value);

        const newSpace = {
            id: Date.now().toString(),
            name: spaceName,
            location: spaceLocation,
            description: spaceDescription,
            amenities: amenities,
            image: this.currentImage,
            dateAdded: new Date().toISOString()
        };

        try {
            this.studySpaces.push(newSpace);
            
            // Save to localStorage immediately
            localStorage.setItem('studySpaces', JSON.stringify(this.studySpaces));
            
            // Save to Firestore
            await saveStudySpacesToFirestore(this.studySpaces);
            
            // Update UI
            this.displayStudySpaces();
            this.resetForm();
            this.showSuccess('Study space saved successfully!');
            
        } catch (error) {
            console.error('Error saving study space:', error);
            this.showError('Failed to save study space. Please try again.');
        }
    }

    /**
     * Delete a study space
     */
    async deleteStudySpace(spaceId) {
        if (!confirm('Are you sure you want to delete this study space?')) {
            return;
        }

        try {
            // Remove from local array
            this.studySpaces = this.studySpaces.filter(space => space.id !== spaceId);
            
            // Update localStorage
            localStorage.setItem('studySpaces', JSON.stringify(this.studySpaces));
            
            // Update Firestore
            await deleteStudySpaceFromFirestore(spaceId, this.studySpaces);
            
            // Update UI
            this.displayStudySpaces();
            this.showSuccess('Study space deleted successfully!');
            
        } catch (error) {
            console.error('Error deleting study space:', error);
            this.showError('Failed to delete study space. Please try again.');
        }
    }

    /**
     * Display study spaces in the UI
     */
    displayStudySpaces() {
        const container = document.getElementById('studySpacesContainer');
        const noSpacesMessage = document.getElementById('noSpacesMessage');

        if (!container) return;

        if (this.studySpaces.length === 0) {
            if (noSpacesMessage) {
                noSpacesMessage.style.display = 'block';
            }
            container.innerHTML = '';
            return;
        }

        if (noSpacesMessage) {
            noSpacesMessage.style.display = 'none';
        }

        container.innerHTML = this.studySpaces.map(space => this.createSpaceCard(space)).join('');
    }

    /**
     * Create HTML for a study space card
     */
    createSpaceCard(space) {
        const amenitiesHtml = space.amenities.map(amenity => 
            `<span class="badge bg-secondary me-1">${amenity}</span>`
        ).join('');

        return `
            <div class="col-md-6 col-lg-4 mb-3">
                <div class="card h-100 study-space-card" id="space${space.id}">
                    <img src="${space.image}" class="card-img-top" alt="${space.name}" style="height: 200px; object-fit: cover;">
                    <div class="card-body">
                        <h5 class="card-title">${space.name}</h5>
                        <p class="card-text">
                            <i class="bi bi-geo-alt me-1"></i>${space.location}
                        </p>
                        <p class="card-text">${space.description}</p>
                        <div class="amenities-display mb-2">
                            ${amenitiesHtml}
                        </div>
                        <small class="text-muted">Added: ${new Date(space.dateAdded).toLocaleDateString()}</small>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-outline-danger btn-sm" onclick="window.studySpacesApp.getModule('studySpacesCore').deleteStudySpace('${space.id}')">
                            <i class="bi bi-trash"></i> Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * Setup file upload handlers
     */
    setupFileUploadHandlers() {
        const uploadArea = document.getElementById('studySpaceUpload');
        const fileInput = document.getElementById('spaceImageInput');

        if (uploadArea && fileInput) {
            uploadArea.addEventListener('click', () => fileInput.click());
            
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('drag-over');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('drag-over');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('drag-over');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    this.handleImageUpload(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    this.handleImageUpload(e.target.files[0]);
                }
            });
        }
    }

    /**
     * Handle image upload
     */
    handleImageUpload(file) {
        if (!file.type.startsWith('image/')) {
            this.showError('Please select an image file');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            this.currentImage = e.target.result;
            this.showUploadForm();
        };
        reader.readAsDataURL(file);
    }

    /**
     * Show the upload form after image selection
     */
    showUploadForm() {
        const uploadArea = document.getElementById('studySpaceUpload');
        const form = document.querySelector('.study-space-form');

        if (uploadArea && form) {
            uploadArea.style.display = 'none';
            form.style.display = 'block';
        }
    }

    /**
     * Reset the form
     */
    resetForm() {
        const form = document.querySelector('.study-space-form');
        const uploadArea = document.getElementById('studySpaceUpload');

        if (form && uploadArea) {
            form.style.display = 'none';
            uploadArea.style.display = 'block';
        }

        // Clear form fields
        ['spaceName', 'spaceLocation', 'spaceDescription'].forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });

        // Clear checkboxes
        document.querySelectorAll('.amenities input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = false;
        });

        this.currentImage = null;
    }

    /**
     * Load schedule settings
     */
    async loadScheduleSettings() {
        const savedSchedule = localStorage.getItem('dailySchedule');
        if (savedSchedule) {
            this.schedule = { ...this.schedule, ...JSON.parse(savedSchedule) };
            this.restoreScheduleInputs();
        }
    }

    /**
     * Restore schedule inputs
     */
    restoreScheduleInputs() {
        const inputs = ['wakeTime', 'sleepTime', 'wakeBuffer', 'sleepBuffer'];
        inputs.forEach(inputId => {
            const element = document.getElementById(inputId);
            if (element && this.schedule[inputId]) {
                element.value = this.schedule[inputId];
            }
        });
    }

    /**
     * Set sync status
     */
    setSyncStatus(isSyncing, error) {
        this.syncStatus.isSyncing = isSyncing;
        this.syncStatus.error = error;
        this.updateSyncStatusUI();
    }

    /**
     * Update sync status UI
     */
    updateSyncStatusUI() {
        const statusIndicator = document.getElementById('syncStatusIndicator');
        const statusText = document.getElementById('syncStatusText');

        if (statusIndicator && statusText) {
            if (this.syncStatus.isSyncing) {
                statusIndicator.innerHTML = '<i class="bi bi-arrow-repeat text-primary"></i>';
                statusText.textContent = 'Syncing...';
            } else if (this.syncStatus.error) {
                statusIndicator.innerHTML = '<i class="bi bi-exclamation-triangle text-warning"></i>';
                statusText.textContent = this.syncStatus.error;
            } else {
                statusIndicator.innerHTML = '<i class="bi bi-check-circle-fill text-success"></i>';
                statusText.textContent = 'Synced with cloud';
            }
        }
    }

    /**
     * Show error message
     */
    showError(message) {
        const errorToast = document.getElementById('errorToast');
        if (errorToast) {
            const toastBody = errorToast.querySelector('.toast-body');
            if (toastBody) {
                toastBody.textContent = message;
                const toast = new bootstrap.Toast(errorToast);
                toast.show();
            }
        }
    }

    /**
     * Show success message
     */
    showSuccess(message) {
        const syncToast = document.getElementById('syncToast');
        if (syncToast) {
            const toastBody = syncToast.querySelector('.toast-body');
            if (toastBody) {
                toastBody.textContent = message;
                const toast = new bootstrap.Toast(syncToast);
                toast.show();
            }
        }
    }
}

// Create singleton instance
const studySpacesCore = new StudySpacesCore();

// Export the main functions
export const loadStudySpaces = () => studySpacesCore.loadStudySpaces();
export const saveStudySpace = () => studySpacesCore.saveStudySpace();
export const deleteStudySpace = (spaceId) => studySpacesCore.deleteStudySpace(spaceId);
export const loadScheduleSettings = () => studySpacesCore.loadScheduleSettings();

// Export the core instance
export { studySpacesCore };
